import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { LogOnlineComponent } from './log-online/log-online.component';
import { LogAuthComponent } from './log-auth/log-auth.component';
import { LogOperComponent } from './log-oper/log-oper.component';

const routes: Routes = [
  { path: 'online', component: LogOnlineComponent },
  { path: 'auth', component: LogAuthComponent },
  { path: 'oper', component: LogOperComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MonitorRoutingModule {}
