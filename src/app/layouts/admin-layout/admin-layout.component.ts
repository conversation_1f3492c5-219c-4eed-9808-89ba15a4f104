import { BidiModule } from '@angular/cdk/bidi';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Component, OnDestroy, ViewChild, ViewEncapsulation, inject } from '@angular/core';
import { <PERSON><PERSON><PERSON>av, Mat<PERSON>idenavContent, MatSidenavModule } from '@angular/material/sidenav';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { NgProgressbar } from 'ngx-progressbar';
import { NgProgressRouter } from 'ngx-progressbar/router';
import { Subscription, filter } from 'rxjs';

import { AppSettings, SettingsService } from '@/services';
import { HeaderComponent } from '@/components/header/header.component';
import { SidebarComponent } from '@/components/sidebar/sidebar.component';
// import { TopmenuComponent } from '@/components/topmenu/topmenu.component';
import { SidebarNoticeComponent } from '@/components/sidebar-notice/sidebar-notice.component';

const MOBILE_MEDIAQUERY = 'screen and (max-width: 599px)';
const TABLET_MEDIAQUERY = 'screen and (min-width: 600px) and (max-width: 959px)';
const MONITOR_MEDIAQUERY = 'screen and (min-width: 960px)';

@Component({
  selector: 'app-admin-layout',
  templateUrl: './admin-layout.component.html',
  styleUrl: './admin-layout.component.scss',
  encapsulation: ViewEncapsulation.None,
  imports: [
    RouterOutlet,
    BidiModule,
    MatSidenavModule,
    NgProgressbar,
    NgProgressRouter,
    HeaderComponent,
    // TopmenuComponent,
    SidebarComponent,
    SidebarNoticeComponent,
  ],
  host: {
    '[class.lsx-content-width-fix]': 'contentWidthFix',
    '[class.lsx-sidenav-collapsed-fix]': 'collapsedWidthFix',
  }
})
export class AdminLayoutComponent implements OnDestroy {
  @ViewChild('sidenav', { static: true }) sidenav!: MatSidenav;
  @ViewChild('content', { static: true }) content!: MatSidenavContent;

  private readonly breakpointObserver = inject(BreakpointObserver);
  private readonly router = inject(Router);
  private readonly settings = inject(SettingsService);

  options = this.settings.options;

  get themeColor() {
    return this.settings.getThemeColor();
  }

  // 手机模式
  get isOver() {
    return this.isMobileScreen;
  }

  private isMobileScreen = false;

  private isContentWidthFixed = true;

  // 固定内容
  get contentWidthFix() {
    return (
      this.isContentWidthFixed &&
      this.options.navPos === 'side' &&
      this.options.sidenavOpened &&
      !this.isOver
    );
  }

  // 固定侧边栏
  get collapsedWidthFix() {
    return (
      this.isCollapsedWidthFixed &&
      (this.options.navPos === 'top' || (this.options.sidenavOpened && this.isOver))
    );
  }

  private isCollapsedWidthFixed = false;

  private layoutChangesSubscription = Subscription.EMPTY;

  constructor() {
    this.layoutChangesSubscription = this.breakpointObserver.observe([
      // Breakpoints.XSmall,   // <= 599.99px
      // Breakpoints.Small,    // >= 600px && <= 959.99px
      // Breakpoints.Medium,   // >= 960px && <= 1279.99px
      // Breakpoints.Large,    // >= 1280px && <= 1919.99px
      // Breakpoints.XLarge,   // >= 1920px
      MOBILE_MEDIAQUERY,
      TABLET_MEDIAQUERY,
      MONITOR_MEDIAQUERY
    ]).subscribe((state: BreakpointState) => {
      // SidenavOpened must be reset true when layout changes
      this.options.sidenavOpened = true;

      this.isMobileScreen = state.breakpoints[MOBILE_MEDIAQUERY];
      this.options.sidenavCollapsed = state.breakpoints[TABLET_MEDIAQUERY];
      this.isContentWidthFixed = state.breakpoints[MONITOR_MEDIAQUERY];
    });

    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {
      if (this.isOver) {
        this.sidenav.close();
      }
      this.content.scrollTo({ top: 0 });
    });
  }

  ngOnDestroy() {
    this.layoutChangesSubscription.unsubscribe();
  }

  toggleCollapsed() {
    this.isContentWidthFixed = false;
    this.options.sidenavCollapsed = !this.options.sidenavCollapsed;
    this.resetCollapsedState();
  }

  // TODO: Trigger when transition end
  resetCollapsedState(timer = 400) {
    setTimeout(() => this.settings.setOptions(this.options), timer);
  }

  onSidenavClosedStart() {
    this.isContentWidthFixed = false;
  }

  onSidenavOpenedChange(isOpened: boolean) {
    this.isCollapsedWidthFixed = !this.isOver;
    this.options.sidenavOpened = isOpened;
    this.settings.setOptions(this.options);
  }

  updateOptions(options: AppSettings) {
    this.options = options;
    this.settings.setOptions(options);
    this.settings.setDirection();
    this.settings.setTheme();
  }
}
