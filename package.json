{"name": "ls-manage", "version": "0.0.1", "author": "<PERSON>ev<PERSON>r(<EMAIL>)", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "build:release": "ng build --configuration production --base-href=/ls/", "watch": "ng build --watch --configuration development", "hmr": "ng serve --hmr --host 0.0.0.0 --o --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "~20.0.5", "@angular/cdk": "~20.0.4", "@angular/common": "~20.0.5", "@angular/compiler": "~20.0.5", "@angular/core": "~20.0.5", "@angular/forms": "~20.0.5", "@angular/material": "~20.0.4", "@angular/material-date-fns-adapter": "~20.0.4", "@angular/platform-browser": "~20.0.5", "@angular/platform-browser-dynamic": "~20.0.5", "@angular/router": "~20.0.5", "@ngbracket/ngx-layout": "^19.0.0", "@ngx-formly/core": "^7.0.0", "@ngx-formly/material": "^7.0.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@ngxs/store": "^20.0.2", "date-fns": "^4.1.0", "material-symbols": "^0.31.9", "ngx-permissions": "^19.0.0", "ngx-progressbar": "^14.0.0", "ngx-scrollbar": "^18.0.0", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.2", "screenfull": "^6.0.2", "tslib": "^2.8.1", "zone.js": "~0.15.1"}, "devDependencies": {"@angular/build": "^20.0.4", "@angular/cli": "^20.0.4", "@angular/compiler-cli": "^20.0.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/jasmine": "~5.1.8", "jasmine-core": "~5.8.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.3"}, "packageManager": "pnpm@10.12.4"}