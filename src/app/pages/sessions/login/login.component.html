<div class="w-full h-full" fxLayout="row wrap">
  <mat-card class="mat-elevation-z4 m-auto" style="max-width: 380px;">
    <mat-card-title class="text-center m-b-24">{{'login.title' | translate}}!</mat-card-title>
    <form class="form-field-full" [formGroup]="loginForm">
      <!-- 用户名 -->
      <mat-form-field appearance="outline">
        <mat-label>{{'login.username' | translate}}</mat-label>
        <input matInput
          [placeholder]="'login.username' | translate"
          formControlName="username"
          required
          />
        <mat-icon fontSet="material-symbols-rounded" matPrefix>person</mat-icon>
        @if (username.value) {
          <button matSuffix
            mat-button
            mat-icon-button
            aria-label="清除"
            (click)="username.setValue('')"
            >
            <mat-icon fontSet="material-symbols-rounded">close</mat-icon>
          </button>
        }
        @if (username.invalid) {
          <mat-error>
            @if (username.errors?.['required']) {
              <span>
                {{'login.please_enter' | translate}}
              </span>
            }
            @if (username.errors?.['remote']) {
              <span>
                {{ username.errors?.['remote'] }}
              </span>
            }
          </mat-error>
        }
      </mat-form-field>
      <!-- 密码 -->
      <mat-form-field appearance="outline">
        <mat-label>{{'login.password' | translate}}</mat-label>
        <input matInput
          [type]="pwdHide ? 'password' : 'text'"
          [placeholder]="'login.password' | translate"
          formControlName="password"
          required
          />
        <mat-icon fontSet="material-symbols-rounded" matPrefix>lock</mat-icon>
        <button matSuffix
          mat-icon-button
          (click)="pwdHide = !pwdHide"
          [attr.aria-label]="'Hide password'"
          [attr.aria-pressed]="pwdHide"
          >
          <mat-icon fontSet="material-symbols-rounded">{{pwdHide ? 'visibility_off' : 'visibility'}}</mat-icon>
        </button>
        @if (password.invalid) {
          <mat-error>
            @if (password.errors?.['required']) {
              <span>
                {{'login.please_enter' | translate}}
              </span>
            }
            @if (password.errors?.['remote']) {
              <span>
                {{ password.errors?.['remote'] }}
              </span>
            }
          </mat-error>
        }
      </mat-form-field>
      <!-- 记住我 -->
      <div class="m-y-16" fxLayout="row" fxLayoutAlign="space-between center">
        <mat-checkbox formControlName="rememberme">{{'login.remember_me' | translate}}</mat-checkbox>
        <a routerLink="/auth/changepwd">{{'login.forgot_pwd' | translate}}</a>
      </div>
      <button class="w-full"
        mat-raised-button color="primary"
        [disabled]="loginForm.invalid"
        (click)="login()"
      >{{'login.login' | translate}}</button>
      <p class="note">{{'login.no_account' | translate}}</p>
    </form>
  </mat-card>
</div>
