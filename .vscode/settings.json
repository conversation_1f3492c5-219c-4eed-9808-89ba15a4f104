{
  "catppuccin-icons.associations.extensions": {
    "component.ts": "angular-component",
    "directive.ts": "angular-directive",
    "guard.ts": "angular-guard",
    "pipe.ts": "angular-pipe",
    "service.ts": "angular-service",
    "module.ts": "angular",
  },
  "angular-schematics.schematicsDefaultOptions": {
    "angular-*": {
      "style": "scss",
      "externalTemplate": true
    }
  },
  "debug.javascript.defaultRuntimeExecutable": {
    "pwa-node": "/home/<USER>/.local/share/mise/shims/node"
  },
}
