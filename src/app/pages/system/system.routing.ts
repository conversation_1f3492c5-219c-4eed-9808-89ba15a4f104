import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { UserComponent } from './user/user.component';
import { RoleComponent } from './role/role.component';
import { MenuComponent } from './menu/menu.component';
import { PostComponent } from './post/post.component';
import { DeptComponent } from './dept/dept.component';
import { DictTypeComponent } from './dict-type/dict-type.component';
import { DictDataComponent } from './dict-data/dict-data.component';
import { ConfigComponent } from './config/config.component';
import { NoticeComponent } from './notice/notice.component';

const routes: Routes = [
  { path: 'user', component: UserComponent },
  { path: 'role', component: RoleComponent },
  { path: 'menu', component: MenuComponent },
  { path: 'dept', component: DeptComponent },
  { path: 'post', component: PostComponent },
  { path: 'dict', component: DictTypeComponent },
  { path: 'dict/:id', component: DictDataComponent },
  { path: 'config', component: ConfigComponent },
  { path: 'notice', component: NoticeComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SystemRoutingModule {}
